using System;
using System.Text.RegularExpressions;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.Services;

/// <summary>
/// Parses QEMU log output to identify core types and classify log entries
/// </summary>
public class QemuLogParser
{
    private readonly LoggingService _logger;

    // Regex patterns for identifying core-specific log entries
    private static readonly Regex R5fPattern = new(@"\[R5F\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex A72Pattern = new(@"\[A72\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex C7xPattern = new(@"\[C7X\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex QemuPattern = new(@"\[QEMU\]", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    // Additional patterns for core identification without explicit tags
    private static readonly Regex R5fKeywordPattern = new(@"\b(real-time|interrupt|safety|critical|R5F)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex A72KeywordPattern = new(@"\b(linux|kernel|application|user\s+space|A72)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);
    private static readonly Regex C7xKeywordPattern = new(@"\b(dsp|signal|processing|C7X)\b", RegexOptions.Compiled | RegexOptions.IgnoreCase);

    public QemuLogParser(LoggingService logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Parses a QEMU log line and creates a TerminalLine with appropriate core classification
    /// </summary>
    /// <param name="logText">The raw log text from QEMU</param>
    /// <param name="isErrorOutput">Whether this is from stderr (true) or stdout (false)</param>
    /// <returns>A TerminalLine with the parsed information</returns>
    public TerminalLine ParseLogLine(string logText, bool isErrorOutput = false)
    {
        if (string.IsNullOrWhiteSpace(logText))
        {
            return TerminalLine.CreateSystemMessage(CoreType.All, "Empty log line received");
        }

        try
        {
            // Determine the source core based on log content
            var sourceCore = IdentifySourceCore(logText);
            
            // Determine line type
            var lineType = isErrorOutput ? TerminalLineType.ErrorOutput : TerminalLineType.StandardOutput;
            
            // Check if this is a QEMU system message
            if (QemuPattern.IsMatch(logText))
            {
                lineType = TerminalLineType.QemuSystemMessage;
            }

            // Create the terminal line
            var terminalLine = new TerminalLine
            {
                Timestamp = DateTime.Now,
                Text = CleanLogText(logText),
                LineType = lineType,
                SourceCore = sourceCore
            };

            return terminalLine;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error parsing QEMU log line '{logText}': {ex.Message}");
            return TerminalLine.CreateErrorOutput(CoreType.All, $"Log parsing error: {ex.Message}");
        }
    }

    /// <summary>
    /// Identifies which core generated the log entry based on content analysis
    /// </summary>
    /// <param name="logText">The log text to analyze</param>
    /// <returns>The identified core type</returns>
    private CoreType IdentifySourceCore(string logText)
    {
        // First, check for explicit core tags
        if (R5fPattern.IsMatch(logText))
            return CoreType.R5F;
        
        if (A72Pattern.IsMatch(logText))
            return CoreType.A72;
        
        if (C7xPattern.IsMatch(logText))
            return CoreType.C7x;

        // If no explicit tags, use keyword-based classification
        var r5fScore = CountMatches(R5fKeywordPattern, logText);
        var a72Score = CountMatches(A72KeywordPattern, logText);
        var c7xScore = CountMatches(C7xKeywordPattern, logText);

        // Return the core with the highest keyword score
        if (r5fScore > a72Score && r5fScore > c7xScore)
            return CoreType.R5F;
        
        if (a72Score > r5fScore && a72Score > c7xScore)
            return CoreType.A72;
        
        if (c7xScore > r5fScore && c7xScore > a72Score)
            return CoreType.C7x;

        // Default to All if no specific core can be identified
        return CoreType.All;
    }

    /// <summary>
    /// Counts the number of regex matches in the text
    /// </summary>
    /// <param name="pattern">The regex pattern to match</param>
    /// <param name="text">The text to search</param>
    /// <returns>Number of matches found</returns>
    private static int CountMatches(Regex pattern, string text)
    {
        return pattern.Matches(text).Count;
    }

    /// <summary>
    /// Cleans the log text by removing redundant prefixes and formatting
    /// </summary>
    /// <param name="logText">The raw log text</param>
    /// <returns>Cleaned log text</returns>
    private static string CleanLogText(string logText)
    {
        // Remove common QEMU prefixes that might be redundant
        var cleaned = logText.Trim();
        
        // Remove timestamp prefixes if they exist (QEMU might add its own)
        var timestampPattern = new Regex(@"^\[\d{2}:\d{2}:\d{2}\.\d{3}\]\s*", RegexOptions.Compiled);
        cleaned = timestampPattern.Replace(cleaned, "");

        return cleaned;
    }

    /// <summary>
    /// Determines if a log line indicates an error condition
    /// </summary>
    /// <param name="logText">The log text to analyze</param>
    /// <returns>True if the line indicates an error, false otherwise</returns>
    public bool IsErrorLine(string logText)
    {
        if (string.IsNullOrWhiteSpace(logText))
            return false;

        var errorKeywords = new[] { "error", "failed", "exception", "fault", "crash", "abort" };
        var lowerText = logText.ToLowerInvariant();

        foreach (var keyword in errorKeywords)
        {
            if (lowerText.Contains(keyword))
                return true;
        }

        return false;
    }
}
