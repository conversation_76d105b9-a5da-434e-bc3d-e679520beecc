using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Reactive.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Avalonia.Platform.Storage;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private readonly ProcessManager _processManager;
    private readonly LoggingService _logger;
    private readonly QemuManager _qemuManager;
    private readonly QemuLogParser _qemuLogParser;
    private readonly QemuController _qemuController;
    private readonly CoreController _c7xController;

    // Binary path properties
    private string _r5fBinaryPath = string.Empty;
    private string _a72BinaryPath = string.Empty;
    private string _c7xBinaryPath = string.Empty;

    // IPC and command properties
    private string _ipcMessage = string.Empty;

    // UI state properties
    private bool _isEmulationRunning;
    private string _statusMessage = "Ready";
    private TestMode _selectedTestMode = TestMode.IPC;

    // Validation properties
    private string _r5fPathError = string.Empty;
    private string _a72PathError = string.Empty;
    private string _c7xPathError = string.Empty;

    // Checkbox properties
    private bool _sourceAllCores;
    private bool _sourceR5F;
    private bool _sourceA72;
    private bool _sourceC7x;
    private bool _destAllCores;
    private bool _destR5F;
    private bool _destA72;
    private bool _destC7x;



    public MainWindowViewModel()
    {
        _processManager = new ProcessManager();
        _logger = LoggingService.Instance;
        _qemuManager = new QemuManager(_logger);
        _qemuLogParser = new QemuLogParser(_logger);
        _qemuController = new QemuController(_qemuLogParser, _logger);
        _c7xController = new CoreController(CoreType.C7x);

        // Initialize core selection collections
        AvailableCores = new ObservableCollection<CoreType>(CoreTypeExtensions.GetIndividualCores().Append(CoreType.All));
        SelectedSourceCores = new ObservableCollection<CoreType>();
        SelectedDestinationCores = new ObservableCollection<CoreType>();



        // Initialize test mode collection
        AvailableTestModes = new ObservableCollection<TestMode>(TestModeExtensions.GetAllTestModes());

        // Initialize commands
        StartEmulationCommand = ReactiveCommand.CreateFromTask(StartEmulationAsync,
            this.WhenAnyValue(x => x.IsEmulationRunning, x => x.R5FPathValid, x => x.A72PathValid, x => x.C7xPathValid,
                (isRunning, r5fValid, a72Valid, c7xValid) => !isRunning && (
                    (r5fValid && a72Valid) ||  // QEMU emulation (R5F + A72)
                    c7xValid ||                // Direct C7x execution
                    (r5fValid && a72Valid && c7xValid)  // Hybrid (QEMU + C7x)
                )));
        StopEmulationCommand = ReactiveCommand.CreateFromTask(StopEmulationAsync, this.WhenAnyValue(x => x.IsEmulationRunning));
        SendIpcCommand = ReactiveCommand.CreateFromTask(SendIpcMessageAsync, this.WhenAnyValue(x => x.CanSendIpc));
        BrowseR5FBinaryCommand = ReactiveCommand.CreateFromTask(() => BrowseBinaryAsync(CoreType.R5F));
        BrowseA72BinaryCommand = ReactiveCommand.CreateFromTask(() => BrowseBinaryAsync(CoreType.A72));
        BrowseC7xBinaryCommand = ReactiveCommand.CreateFromTask(() => BrowseBinaryAsync(CoreType.C7x));
        ClearTerminalCommand = ReactiveCommand.Create<CoreType>(ClearTerminal);
        ShowHelpCommand = ReactiveCommand.Create(ShowHelp);


        // Subscribe to process manager events
        _processManager.OutputReceived += OnOutputReceived;
        _processManager.ProcessStatusChanged += OnProcessStatusChanged;

        // Subscribe to QEMU controller events
        _qemuController.OutputReceived += OnQemuOutputReceived;
        _qemuController.ProcessExited += OnQemuProcessExited;

        // Subscribe to C7x controller events
        _c7xController.OutputReceived += OnC7xOutputReceived;
        _c7xController.ProcessExited += OnC7xProcessExited;

        // Set up property validation
        this.WhenAnyValue(x => x.R5FBinaryPath)
            .Subscribe(_ => ValidateR5FPath());

        this.WhenAnyValue(x => x.A72BinaryPath)
            .Subscribe(_ => ValidateA72Path());

        this.WhenAnyValue(x => x.C7xBinaryPath)
            .Subscribe(_ => ValidateC7xPath());

        // Log initial validation state
        LogValidationState("Initialization");

        _logger.LogInfo("TDA4 Emulator Control Panel initialized");
    }

    #region Properties

    /// <summary>
    /// Path to the R5F core binary
    /// </summary>
    public string R5FBinaryPath
    {
        get => _r5fBinaryPath;
        set => this.RaiseAndSetIfChanged(ref _r5fBinaryPath, value);
    }

    /// <summary>
    /// Path to the A72 core binary
    /// </summary>
    public string A72BinaryPath
    {
        get => _a72BinaryPath;
        set => this.RaiseAndSetIfChanged(ref _a72BinaryPath, value);
    }

    /// <summary>
    /// Path to the C7x core binary
    /// </summary>
    public string C7xBinaryPath
    {
        get => _c7xBinaryPath;
        set => this.RaiseAndSetIfChanged(ref _c7xBinaryPath, value);
    }

    /// <summary>
    /// IPC message to send between cores
    /// </summary>
    public string IpcMessage
    {
        get => _ipcMessage;
        set
        {
            this.RaiseAndSetIfChanged(ref _ipcMessage, value);
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
    }

    /// <summary>
    /// Currently selected test mode
    /// </summary>
    public TestMode SelectedTestMode
    {
        get => _selectedTestMode;
        set => this.RaiseAndSetIfChanged(ref _selectedTestMode, value);
    }

    /// <summary>
    /// Indicates whether emulation is currently running
    /// </summary>
    public bool IsEmulationRunning
    {
        get => _isEmulationRunning;
        private set
        {
            this.RaiseAndSetIfChanged(ref _isEmulationRunning, value);
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
    }

    /// <summary>
    /// Current status message
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    /// <summary>
    /// Available core types for selection
    /// </summary>
    public ObservableCollection<CoreType> AvailableCores { get; }

    /// <summary>
    /// Selected source cores for IPC
    /// </summary>
    public ObservableCollection<CoreType> SelectedSourceCores { get; }

    /// <summary>
    /// Selected destination cores for IPC
    /// </summary>
    public ObservableCollection<CoreType> SelectedDestinationCores { get; }

    /// <summary>
    /// Available test modes
    /// </summary>
    public ObservableCollection<TestMode> AvailableTestModes { get; }

    /// <summary>
    /// Validation error for R5F binary path
    /// </summary>
    public string R5FPathError
    {
        get => _r5fPathError;
        private set => this.RaiseAndSetIfChanged(ref _r5fPathError, value);
    }

    /// <summary>
    /// Indicates if R5F binary path is valid
    /// </summary>
    public bool R5FPathValid => !string.IsNullOrWhiteSpace(R5FBinaryPath) && string.IsNullOrEmpty(R5FPathError);

    /// <summary>
    /// Validation error for A72 binary path
    /// </summary>
    public string A72PathError
    {
        get => _a72PathError;
        private set => this.RaiseAndSetIfChanged(ref _a72PathError, value);
    }

    /// <summary>
    /// Indicates if A72 binary path is valid
    /// </summary>
    public bool A72PathValid => !string.IsNullOrWhiteSpace(A72BinaryPath) && string.IsNullOrEmpty(A72PathError);

    /// <summary>
    /// Validation error for C7x binary path
    /// </summary>
    public string C7xPathError
    {
        get => _c7xPathError;
        private set => this.RaiseAndSetIfChanged(ref _c7xPathError, value);
    }

    /// <summary>
    /// Indicates if C7x binary path is valid
    /// </summary>
    public bool C7xPathValid => !string.IsNullOrWhiteSpace(C7xBinaryPath) && string.IsNullOrEmpty(C7xPathError);

    // Source core checkbox properties
    public bool SourceAllCores
    {
        get => _sourceAllCores;
        set
        {
            this.RaiseAndSetIfChanged(ref _sourceAllCores, value);
            UpdateSourceCoreSelection();
        }
    }

    public bool SourceR5F
    {
        get => _sourceR5F;
        set
        {
            this.RaiseAndSetIfChanged(ref _sourceR5F, value);
            UpdateSourceCoreSelection();
        }
    }

    public bool SourceA72
    {
        get => _sourceA72;
        set
        {
            this.RaiseAndSetIfChanged(ref _sourceA72, value);
            UpdateSourceCoreSelection();
        }
    }

    public bool SourceC7x
    {
        get => _sourceC7x;
        set
        {
            this.RaiseAndSetIfChanged(ref _sourceC7x, value);
            UpdateSourceCoreSelection();
        }
    }

    // Destination core checkbox properties
    public bool DestAllCores
    {
        get => _destAllCores;
        set
        {
            this.RaiseAndSetIfChanged(ref _destAllCores, value);
            UpdateDestinationCoreSelection();
        }
    }

    public bool DestR5F
    {
        get => _destR5F;
        set
        {
            this.RaiseAndSetIfChanged(ref _destR5F, value);
            UpdateDestinationCoreSelection();
        }
    }

    public bool DestA72
    {
        get => _destA72;
        set
        {
            this.RaiseAndSetIfChanged(ref _destA72, value);
            UpdateDestinationCoreSelection();
        }
    }

    public bool DestC7x
    {
        get => _destC7x;
        set
        {
            this.RaiseAndSetIfChanged(ref _destC7x, value);
            UpdateDestinationCoreSelection();
        }
    }



    /// <summary>
    /// Terminal output for R5F core as formatted text (filtered from QEMU output)
    /// </summary>
    public string R5FTerminalOutput
    {
        get
        {
            try
            {
                var lines = _qemuController.TerminalOutput.Where(line => line.SourceCore == CoreType.R5F || line.SourceCore == CoreType.All);
                return ConvertTerminalLinesToText(new ObservableCollection<TerminalLine>(lines));
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting R5F terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Terminal output for A72 core as formatted text (filtered from QEMU output)
    /// </summary>
    public string A72TerminalOutput
    {
        get
        {
            try
            {
                var lines = _qemuController.TerminalOutput.Where(line => line.SourceCore == CoreType.A72 || line.SourceCore == CoreType.All);
                return ConvertTerminalLinesToText(new ObservableCollection<TerminalLine>(lines));
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting A72 terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Terminal output for C7x core as formatted text (from direct C7x process)
    /// </summary>
    public string C7xTerminalOutput
    {
        get
        {
            try
            {
                // Get output from direct C7x controller (not QEMU)
                return ConvertTerminalLinesToText(_c7xController.TerminalOutput);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting C7x terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Determines if emulation can be started (supports hybrid emulation modes)
    /// </summary>
    public bool CanStartEmulation => !IsEmulationRunning && (
        (R5FPathValid && A72PathValid) ||  // QEMU emulation (R5F + A72)
        C7xPathValid ||                    // Direct C7x execution
        (R5FPathValid && A72PathValid && C7xPathValid)  // Hybrid (QEMU + C7x)
    );

    /// <summary>
    /// Determines if IPC message can be sent
    /// </summary>
    public bool CanSendIpc
    {
        get
        {
            var isRunning = IsEmulationRunning;
            var hasSourceCores = SelectedSourceCores.Any();
            var hasDestCores = SelectedDestinationCores.Any();
            var hasMessage = !string.IsNullOrWhiteSpace(IpcMessage);

            return isRunning && hasSourceCores && hasDestCores && hasMessage;
        }
    }

    #endregion

    #region Commands

    public ReactiveCommand<Unit, Unit> StartEmulationCommand { get; }
    public ReactiveCommand<Unit, Unit> StopEmulationCommand { get; }
    public ReactiveCommand<Unit, Unit> SendIpcCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseR5FBinaryCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseA72BinaryCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseC7xBinaryCommand { get; }
    public ReactiveCommand<CoreType, Unit> ClearTerminalCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowHelpCommand { get; }


    #endregion

    #region Command Implementations

    private async Task StartEmulationAsync()
    {
        try
        {
            StatusMessage = "Starting hybrid emulation...";
            _logger.LogInfo("Starting TDA4 hybrid emulation");

            bool qemuStarted = false;
            bool c7xStarted = false;
            var startedProcesses = new List<string>();

            // Determine emulation mode and start appropriate processes
            bool shouldStartQemu = R5FPathValid && A72PathValid;
            bool shouldStartC7x = C7xPathValid;

            if (!shouldStartQemu && !shouldStartC7x)
            {
                StatusMessage = "No valid binary paths configured for emulation";
                _logger.LogWarning("Attempted to start emulation with no valid binary paths");
                return;
            }

            // Start QEMU emulation for R5F + A72 cores
            if (shouldStartQemu)
            {
                _logger.LogInfo("Starting QEMU emulation for R5F and A72 cores");

                // Get QEMU binary path (default or custom)
                string qemuBinaryPath;
                try
                {
                    qemuBinaryPath = _qemuManager.GetQemuBinaryPath();
                }
                catch (FileNotFoundException ex)
                {
                    StatusMessage = "QEMU binary not found";
                    _logger.LogError($"QEMU binary not found: {ex.Message}");
                    return;
                }

                // Configure QEMU controller (only R5F and A72)
                _qemuController.QemuBinaryPath = qemuBinaryPath;
                _qemuController.R5FBinaryPath = R5FBinaryPath;
                _qemuController.A72BinaryPath = A72BinaryPath;

                // Start QEMU process
                qemuStarted = await _qemuController.StartAsync();
                if (qemuStarted)
                {
                    startedProcesses.Add("QEMU (R5F + A72)");
                    _logger.LogInfo("QEMU emulation started successfully");
                }
                else
                {
                    _logger.LogError("Failed to start QEMU emulation");
                }
            }

            // Start direct C7x execution
            if (shouldStartC7x)
            {
                _logger.LogInfo("Starting direct C7x core execution");

                // Configure C7x controller
                _c7xController.BinaryPath = C7xBinaryPath;

                // Start C7x process
                c7xStarted = await _c7xController.StartAsync();
                if (c7xStarted)
                {
                    startedProcesses.Add("C7x (Direct)");
                    _logger.LogInfo("C7x direct execution started successfully");
                }
                else
                {
                    _logger.LogError("Failed to start C7x direct execution");
                }
            }

            // Update UI state based on results
            bool anyStarted = qemuStarted || c7xStarted;
            if (anyStarted)
            {
                IsEmulationRunning = true;
                var processesText = string.Join(" + ", startedProcesses);
                StatusMessage = $"Hybrid emulation started: {processesText}";
                _logger.LogInfo($"Hybrid emulation started successfully: {processesText}");
            }
            else
            {
                StatusMessage = "Failed to start any emulation processes";
                _logger.LogError("Failed to start any emulation processes");

                // Clean up any partially started processes
                await StopEmulationAsync();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error starting hybrid emulation";
            _logger.LogError("Error starting hybrid emulation", ex);

            // Clean up any partially started processes
            await StopEmulationAsync();
        }
    }

    private async Task StopEmulationAsync()
    {
        try
        {
            StatusMessage = "Stopping hybrid emulation...";
            _logger.LogInfo("Stopping TDA4 hybrid emulation");

            var stopTasks = new List<Task>();
            var stoppedProcesses = new List<string>();

            // Stop QEMU process if running
            if (_qemuController.IsRunning)
            {
                _logger.LogInfo("Stopping QEMU emulation");
                stopTasks.Add(_qemuController.StopAsync());
                stoppedProcesses.Add("QEMU");
            }

            // Stop C7x process if running
            if (_c7xController.IsRunning)
            {
                _logger.LogInfo("Stopping C7x direct execution");
                stopTasks.Add(_c7xController.StopAsync());
                stoppedProcesses.Add("C7x");
            }

            // Wait for all processes to stop
            if (stopTasks.Any())
            {
                await Task.WhenAll(stopTasks);
            }

            IsEmulationRunning = false;

            if (stoppedProcesses.Any())
            {
                var processesText = string.Join(" + ", stoppedProcesses);
                StatusMessage = $"Hybrid emulation stopped: {processesText}";
                _logger.LogInfo($"Hybrid emulation stopped successfully: {processesText}");
            }
            else
            {
                StatusMessage = "No emulation processes were running";
                _logger.LogInfo("No emulation processes were running");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error stopping hybrid emulation";
            _logger.LogError("Error stopping hybrid emulation", ex);

            // Force update the running state even if there was an error
            IsEmulationRunning = false;
        }
    }

    private async Task SendIpcMessageAsync()
    {
        try
        {
            if (!IsEmulationRunning)
            {
                StatusMessage = "No emulation processes are running";
                return;
            }

            if (!SelectedSourceCores.Any())
            {
                StatusMessage = "No source cores selected for IPC command";
                return;
            }

            // Determine which processes to target based on selected source cores
            var routingResult = DetermineCommandRouting(SelectedSourceCores);

            if (!routingResult.ShouldSendToQemu && !routingResult.ShouldSendToC7x)
            {
                StatusMessage = "No running processes correspond to selected source cores";
                return;
            }

            var sendTasks = new List<Task<bool>>();
            var targetProcesses = new List<string>();
            var targetCores = new List<string>();

            // Send command to QEMU process if required and running
            if (routingResult.ShouldSendToQemu && _qemuController.IsRunning)
            {
                sendTasks.Add(_qemuController.SendCommandAsync(IpcMessage));
                targetProcesses.Add("QEMU");
                targetCores.AddRange(routingResult.QemuCores);
            }

            // Send command to C7x process if required and running
            if (routingResult.ShouldSendToC7x && _c7xController.IsRunning)
            {
                sendTasks.Add(_c7xController.SendCommandAsync(IpcMessage));
                targetProcesses.Add("C7x");
                targetCores.Add("C7x");
            }

            if (!sendTasks.Any())
            {
                var missingProcesses = new List<string>();
                if (routingResult.ShouldSendToQemu && !_qemuController.IsRunning)
                    missingProcesses.Add("QEMU");
                if (routingResult.ShouldSendToC7x && !_c7xController.IsRunning)
                    missingProcesses.Add("C7x");

                StatusMessage = $"Required processes not running: {string.Join(", ", missingProcesses)}";
                return;
            }

            // Wait for all commands to be sent
            var results = await Task.WhenAll(sendTasks);
            var successCount = results.Count(r => r);
            var totalCount = results.Length;

            if (successCount > 0)
            {
                var processesText = string.Join(" + ", targetProcesses.Take(successCount));
                var coresText = string.Join(", ", targetCores);
                StatusMessage = $"Command sent to {processesText} for cores: {coresText} ({successCount}/{totalCount})";
                var commandText = IpcMessage; // Store before clearing
                IpcMessage = string.Empty; // Clear after sending
                _logger.LogInfo($"Command sent successfully to {successCount}/{totalCount} processes for cores [{coresText}]: {commandText}");
            }
            else
            {
                StatusMessage = "Failed to send command to any processes";
                _logger.LogWarning("Failed to send command to any processes");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error sending command to processes";
            _logger.LogError("Error sending command to processes", ex);
        }
    }





    private async Task BrowseBinaryAsync(CoreType coreType)
    {
        try
        {
            StatusMessage = $"Browse for {coreType.GetDisplayName()} binary...";

            // Get the main window to access the storage provider
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow?.StorageProvider != null)
            {
                var files = await mainWindow.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                {
                    Title = $"Select {coreType.GetDisplayName()} Binary",
                    AllowMultiple = false,
                    FileTypeFilter = GetExecutableFileTypes()
                });

                if (files.Count > 0)
                {
                    var selectedPath = files[0].Path.LocalPath;

                    // Update the appropriate binary path property
                    switch (coreType)
                    {
                        case CoreType.R5F:
                            R5FBinaryPath = selectedPath;
                            break;
                        case CoreType.A72:
                            A72BinaryPath = selectedPath;
                            break;
                        case CoreType.C7x:
                            C7xBinaryPath = selectedPath;
                            break;
                    }

                    StatusMessage = $"{coreType.GetDisplayName()} binary selected";
                    _logger.LogInfo($"Binary selected for {coreType}: {selectedPath}");

                    // Log validation state after file selection
                    LogValidationState($"After {coreType} file selection");
                }
                else
                {
                    StatusMessage = "File selection cancelled";
                }
            }
            else
            {
                StatusMessage = "File browser not available";
                _logger.LogWarning("Storage provider not available for file browsing");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error browsing for binary";
            _logger.LogError("Error browsing for binary", ex);
        }
    }

    private List<FilePickerFileType> GetExecutableFileTypes()
    {
        var fileTypes = new List<FilePickerFileType>();

        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            // Windows executable file types
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*.exe", "*.bat", "*.cmd" }
            });
        }
        else
        {
            // Linux/Unix executable file types
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*" }  // Show all files on Linux since executables often have no extension
            });

            // Common executable patterns for Linux
            fileTypes.Add(new FilePickerFileType("Binary Files")
            {
                Patterns = new[] { "*.bin", "*.elf", "*.out" }
            });
        }

        // All files option for all platforms
        fileTypes.Add(new FilePickerFileType("All Files")
        {
            Patterns = new[] { "*" }  // Use "*" instead of "*.*" to show files without extensions
        });

        return fileTypes;
    }

    private void ClearTerminal(CoreType coreType)
    {
        try
        {
            _processManager.ClearTerminalOutput(coreType);

            // Force immediate UI update on the UI thread
            Avalonia.Threading.Dispatcher.UIThread.Post(() =>
            {
                try
                {
                    // Update specific terminal outputs based on the core type
                    if (coreType == CoreType.All)
                    {
                        this.RaisePropertyChanged(nameof(R5FTerminalOutput));
                        this.RaisePropertyChanged(nameof(A72TerminalOutput));
                        this.RaisePropertyChanged(nameof(C7xTerminalOutput));
                    }
                    else if (coreType == CoreType.R5F)
                    {
                        this.RaisePropertyChanged(nameof(R5FTerminalOutput));
                    }
                    else if (coreType == CoreType.A72)
                    {
                        this.RaisePropertyChanged(nameof(A72TerminalOutput));
                    }
                    else if (coreType == CoreType.C7x)
                    {
                        this.RaisePropertyChanged(nameof(C7xTerminalOutput));
                    }
                }
                catch (Exception uiEx)
                {
                    _logger.LogError("Error updating UI after terminal clear", uiEx);
                }
            });

            StatusMessage = $"Terminal cleared for {coreType.GetDisplayName()}";
            _logger.LogInfo($"Terminal cleared for {coreType}");
        }
        catch (Exception ex)
        {
            StatusMessage = "Error clearing terminal";
            _logger.LogError("Error clearing terminal", ex);
        }
    }

    private void ShowHelp()
    {
        try
        {
            StatusMessage = "Help dialog opened";
            _logger.LogInfo("Help dialog requested");

            var helpDialog = new TDA4_Emulator.Views.HelpDialog();

            // Get the main window to show dialog
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow != null)
            {
                helpDialog.ShowDialog(mainWindow);
            }
            else
            {
                helpDialog.Show();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error showing help";
            _logger.LogError("Error showing help", ex);
        }
    }



    private void UpdateSourceCoreSelection()
    {
        try
        {
            SelectedSourceCores.Clear();

            if (SourceAllCores)
            {
                SelectedSourceCores.Add(CoreType.All);
                // When "All" is selected, uncheck individual cores
                _sourceR5F = false;
                _sourceA72 = false;
                _sourceC7x = false;
                this.RaisePropertyChanged(nameof(SourceR5F));
                this.RaisePropertyChanged(nameof(SourceA72));
                this.RaisePropertyChanged(nameof(SourceC7x));
            }
            else
            {
                if (SourceR5F) SelectedSourceCores.Add(CoreType.R5F);
                if (SourceA72) SelectedSourceCores.Add(CoreType.A72);
                if (SourceC7x) SelectedSourceCores.Add(CoreType.C7x);

                // If individual cores are selected, uncheck "All"
                if ((SourceR5F || SourceA72 || SourceC7x) && _sourceAllCores)
                {
                    _sourceAllCores = false;
                    this.RaisePropertyChanged(nameof(SourceAllCores));
                }
            }

            _logger.LogInfo($"Source cores updated: {SelectedSourceCores.Count} items: [{string.Join(", ", SelectedSourceCores)}]");
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
        catch (Exception ex)
        {
            _logger.LogError("Error updating source core selection", ex);
        }
    }

    private void UpdateDestinationCoreSelection()
    {
        try
        {
            SelectedDestinationCores.Clear();

            if (DestAllCores)
            {
                SelectedDestinationCores.Add(CoreType.All);
                // When "All" is selected, uncheck individual cores
                _destR5F = false;
                _destA72 = false;
                _destC7x = false;
                this.RaisePropertyChanged(nameof(DestR5F));
                this.RaisePropertyChanged(nameof(DestA72));
                this.RaisePropertyChanged(nameof(DestC7x));
            }
            else
            {
                if (DestR5F) SelectedDestinationCores.Add(CoreType.R5F);
                if (DestA72) SelectedDestinationCores.Add(CoreType.A72);
                if (DestC7x) SelectedDestinationCores.Add(CoreType.C7x);

                // If individual cores are selected, uncheck "All"
                if ((DestR5F || DestA72 || DestC7x) && _destAllCores)
                {
                    _destAllCores = false;
                    this.RaisePropertyChanged(nameof(DestAllCores));
                }
            }

            _logger.LogInfo($"Destination cores updated: {SelectedDestinationCores.Count} items: [{string.Join(", ", SelectedDestinationCores)}]");
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
        catch (Exception ex)
        {
            _logger.LogError("Error updating destination core selection", ex);
        }
    }

    /// <summary>
    /// Safely converts a collection of TerminalLine objects to formatted text
    /// </summary>
    private string ConvertTerminalLinesToText(ObservableCollection<TerminalLine>? lines)
    {
        try
        {
            if (lines == null || lines.Count == 0)
            {
                return string.Empty;
            }

            var sb = new StringBuilder();
            foreach (var line in lines)
            {
                if (line != null && !string.IsNullOrEmpty(line.FormattedText))
                {
                    sb.AppendLine(line.FormattedText);
                }
            }
            return sb.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError("Error converting terminal lines to text", ex);
            return string.Empty;
        }
    }

    #endregion

    #region Validation Methods

    private void ValidateR5FPath()
    {
        var oldValid = R5FPathValid;

        if (string.IsNullOrWhiteSpace(R5FBinaryPath))
        {
            R5FPathError = string.Empty;
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogInfo($"R5F path validation: Empty path, Valid={R5FPathValid}");
            return;
        }

        if (!File.Exists(R5FBinaryPath))
        {
            R5FPathError = "File does not exist";
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogWarning($"R5F path validation: File not found '{R5FBinaryPath}', Valid={R5FPathValid}");
            return;
        }

        if (!IsExecutableFile(R5FBinaryPath))
        {
            R5FPathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogWarning($"R5F path validation: Not executable '{R5FBinaryPath}', Valid={R5FPathValid}");
            return;
        }

        R5FPathError = string.Empty;
        this.RaisePropertyChanged(nameof(R5FPathValid));

        if (oldValid != R5FPathValid)
        {
            _logger.LogInfo($"R5F path validation: '{R5FBinaryPath}' is valid, Valid={R5FPathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private void ValidateA72Path()
    {
        var oldValid = A72PathValid;

        if (string.IsNullOrWhiteSpace(A72BinaryPath))
        {
            A72PathError = string.Empty;
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogInfo($"A72 path validation: Empty path, Valid={A72PathValid}");
            return;
        }

        if (!File.Exists(A72BinaryPath))
        {
            A72PathError = "File does not exist";
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogWarning($"A72 path validation: File not found '{A72BinaryPath}', Valid={A72PathValid}");
            return;
        }

        if (!IsExecutableFile(A72BinaryPath))
        {
            A72PathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogWarning($"A72 path validation: Not executable '{A72BinaryPath}', Valid={A72PathValid}");
            return;
        }

        A72PathError = string.Empty;
        this.RaisePropertyChanged(nameof(A72PathValid));

        if (oldValid != A72PathValid)
        {
            _logger.LogInfo($"A72 path validation: '{A72BinaryPath}' is valid, Valid={A72PathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private void ValidateC7xPath()
    {
        var oldValid = C7xPathValid;

        if (string.IsNullOrWhiteSpace(C7xBinaryPath))
        {
            C7xPathError = string.Empty;
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogInfo($"C7x path validation: Empty path, Valid={C7xPathValid}");
            return;
        }

        if (!File.Exists(C7xBinaryPath))
        {
            C7xPathError = "File does not exist";
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogWarning($"C7x path validation: File not found '{C7xBinaryPath}', Valid={C7xPathValid}");
            return;
        }

        if (!IsExecutableFile(C7xBinaryPath))
        {
            C7xPathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogWarning($"C7x path validation: Not executable '{C7xBinaryPath}', Valid={C7xPathValid}");
            return;
        }

        C7xPathError = string.Empty;
        this.RaisePropertyChanged(nameof(C7xPathValid));

        if (oldValid != C7xPathValid)
        {
            _logger.LogInfo($"C7x path validation: '{C7xBinaryPath}' is valid, Valid={C7xPathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private bool IsExecutableFile(string filePath)
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".exe" || extension == ".bat" || extension == ".cmd";
        }
        else if (Environment.OSVersion.Platform == PlatformID.Unix)
        {
            // On Unix-like systems, check if file has execute permissions
            try
            {
                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                    return false;

                // Check if file is executable by trying to get Unix file permissions
                // This is a more robust check for Linux systems
                var result = System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "test",
                    Arguments = $"-x \"{filePath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                });

                if (result != null)
                {
                    result.WaitForExit(1000); // Wait max 1 second
                    return result.ExitCode == 0;
                }
            }
            catch
            {
                // Fallback: if we can't check permissions, assume it's executable if it's a regular file
                // This handles cases where the 'test' command might not be available
            }

            // Fallback check: accept files without extension or common executable patterns
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();
            var fileExtension = Path.GetExtension(filePath).ToLowerInvariant();

            // Accept files with no extension (common for Linux executables)
            if (string.IsNullOrEmpty(fileExtension))
                return true;

            // Accept common executable extensions
            return fileExtension == ".bin" || fileExtension == ".elf" || fileExtension == ".out" ||
                   fileName.Contains("_core") || fileName.Contains("tda4");
        }
        else // Unsupported platform
        {
            _logger.LogWarning($"Unsupported platform: {Environment.OSVersion.Platform}");
            return false;
        }
    }

    private string GetExecutableErrorMessage()
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            return "File must be .exe, .bat, or .cmd";
        }
        else if (Environment.OSVersion.Platform == PlatformID.Unix)
        {
            return "File must be executable (try: chmod +x filename)";
        } else
        {
            return "Unsupported platform";
        }
    }

    private bool HasValidBinaryPaths()
    {
        return R5FPathValid || A72PathValid || C7xPathValid;
    }

    /// <summary>
    /// Logs the current validation state for debugging
    /// </summary>
    private void LogValidationState(string context)
    {
        _logger.LogInfo($"=== Validation State ({context}) ===");
        _logger.LogInfo($"R5F: Path='{R5FBinaryPath}', Error='{R5FPathError}', Valid={R5FPathValid}");
        _logger.LogInfo($"A72: Path='{A72BinaryPath}', Error='{A72PathError}', Valid={A72PathValid}");
        _logger.LogInfo($"C7x: Path='{C7xBinaryPath}', Error='{C7xPathError}', Valid={C7xPathValid}");
        _logger.LogInfo($"IsEmulationRunning={IsEmulationRunning}");
        _logger.LogInfo($"CanStartEmulation={CanStartEmulation}");
        _logger.LogInfo($"HasValidBinaryPaths={HasValidBinaryPaths()}");
        _logger.LogInfo("=== End Validation State ===");
    }



    /// <summary>
    /// Determines which processes should receive IPC commands based on selected source cores
    /// </summary>
    /// <param name="selectedSourceCores">Collection of selected source cores</param>
    /// <returns>Routing result indicating which processes to target</returns>
    private CommandRoutingResult DetermineCommandRouting(IEnumerable<CoreType> selectedSourceCores)
    {
        var result = new CommandRoutingResult();
        var sourceCores = selectedSourceCores.ToList();

        // Handle "All" selection - expand to individual cores
        if (sourceCores.Contains(CoreType.All))
        {
            sourceCores = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        foreach (var sourceCore in sourceCores)
        {
            switch (sourceCore)
            {
                case CoreType.R5F:
                    result.ShouldSendToQemu = true;
                    result.QemuCores.Add("R5F");
                    break;

                case CoreType.A72:
                    result.ShouldSendToQemu = true;
                    result.QemuCores.Add("A72");
                    break;

                case CoreType.C7x:
                    result.ShouldSendToC7x = true;
                    break;
            }
        }

        _logger.LogInfo($"Command routing determined: QEMU={result.ShouldSendToQemu} (cores: {string.Join(",", result.QemuCores)}), C7x={result.ShouldSendToC7x}");
        return result;
    }

    #endregion

    #region Event Handlers

    private void OnOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notifications for individual terminal outputs
            this.RaisePropertyChanged(nameof(R5FTerminalOutput));
            this.RaisePropertyChanged(nameof(A72TerminalOutput));
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnProcessStatusChanged(object? sender, ProcessStatusChangedEventArgs e)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            IsEmulationRunning = _processManager.IsAnyProcessRunning;

            if (!e.IsRunning && e.ExitCode.HasValue)
            {
                StatusMessage = $"{e.CoreType.GetDisplayName()} exited with code {e.ExitCode}";
            }
        });
    }

    private void OnQemuOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notifications for individual terminal outputs
            // This will cause the filtered terminal outputs to refresh
            this.RaisePropertyChanged(nameof(R5FTerminalOutput));
            this.RaisePropertyChanged(nameof(A72TerminalOutput));
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnQemuProcessExited(object? sender, int exitCode)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Check if any processes are still running
            bool anyRunning = _qemuController.IsRunning || _c7xController.IsRunning;
            if (!anyRunning)
            {
                IsEmulationRunning = false;
            }

            StatusMessage = $"QEMU process exited with code {exitCode}";
            _logger.LogInfo($"QEMU process exited with code {exitCode}");
        });
    }

    private void OnC7xOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notification for C7x terminal output
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnC7xProcessExited(object? sender, int exitCode)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Check if any processes are still running
            bool anyRunning = _qemuController.IsRunning || _c7xController.IsRunning;
            if (!anyRunning)
            {
                IsEmulationRunning = false;
            }

            StatusMessage = $"C7x process exited with code {exitCode}";
            _logger.LogInfo($"C7x process exited with code {exitCode}");
        });
    }

    #endregion

    #region IDisposable

    public override void Dispose()
    {
        _processManager?.Dispose();
        _qemuController?.Dispose();
        _c7xController?.Dispose();
        base.Dispose();
    }

    #endregion
}

/// <summary>
/// Result of command routing analysis for IPC messages
/// </summary>
internal class CommandRoutingResult
{
    /// <summary>
    /// Whether the command should be sent to the QEMU process
    /// </summary>
    public bool ShouldSendToQemu { get; set; }

    /// <summary>
    /// Whether the command should be sent to the C7x process
    /// </summary>
    public bool ShouldSendToC7x { get; set; }

    /// <summary>
    /// List of QEMU cores that should receive the command
    /// </summary>
    public List<string> QemuCores { get; set; } = new();
}