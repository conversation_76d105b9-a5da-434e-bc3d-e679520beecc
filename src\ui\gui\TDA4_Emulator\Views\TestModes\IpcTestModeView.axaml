<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             xmlns:controls="using:TDA4_Emulator.Controls"
             xmlns:converters="using:TDA4_Emulator.Converters"
             xmlns:models="using:TDA4_Emulator.Models"
             x:Class="TDA4_Emulator.Views.TestModes.IpcTestModeView"
             x:DataType="vm:MainWindowViewModel">

    <UserControl.Resources>
        <converters:CollectionContainsConverter x:Key="CollectionContainsConverter"/>
    </UserControl.Resources>

    <Border Background="#F5F5F5" Padding="12">
        <StackPanel Spacing="12">

            <!-- IPC Message Section -->
            <Label Content="Inter-Processor Communication"
                   FontWeight="Bold" FontSize="14" Foreground="#800020"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Source Cores -->
                <StackPanel Grid.Column="0" Margin="0,0,6,0">
                    <Label Content="Source Cores:"/>
                    <Border BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="4" Padding="8">
                        <StackPanel>
                            <CheckBox Content="All Cores" Margin="2"
                                      IsChecked="{Binding SourceAllCores}"/>
                            <CheckBox Content="R5F Core" Margin="2"
                                      IsChecked="{Binding SourceR5F}"/>
                            <CheckBox Content="A72 Core" Margin="2"
                                      IsChecked="{Binding SourceA72}"/>
                            <CheckBox Content="C7x Core" Margin="2"
                                      IsChecked="{Binding SourceC7x}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Destination Cores -->
                <StackPanel Grid.Column="1" Margin="6,0,0,0">
                    <Label Content="Destination Cores:"/>
                    <Border BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="4" Padding="8">
                        <StackPanel>
                            <CheckBox Content="All Cores" Margin="2"
                                      IsChecked="{Binding DestAllCores}"/>
                            <CheckBox Content="R5F Core" Margin="2"
                                      IsChecked="{Binding DestR5F}"/>
                            <CheckBox Content="A72 Core" Margin="2"
                                      IsChecked="{Binding DestA72}"/>
                            <CheckBox Content="C7x Core" Margin="2"
                                      IsChecked="{Binding DestC7x}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>

            <!-- IPC Message Input -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <TextBox Grid.Column="0" Text="{Binding IpcMessage}"
                         Watermark="Enter command/message to send to selected cores..."
                         Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                <Button Grid.Column="1" Content="Send message" Classes="primary"
                        Command="{Binding SendIpcCommand}"
                        IsEnabled="{Binding CanSendIpc}"
                        VerticalAlignment="Stretch" MinWidth="100"
                        HorizontalContentAlignment="Center"
                        VerticalContentAlignment="Center"/>
            </Grid>

            <!-- Instructions -->
            <TextBlock TextWrapping="Wrap" FontSize="11" Foreground="#666666">
                Select source and destination cores, then enter a command or message to send directly to the core processes via standard input.
                The message will be sent as raw text to each selected core's stdin stream.
            </TextBlock>

        </StackPanel>
    </Border>

</UserControl>
