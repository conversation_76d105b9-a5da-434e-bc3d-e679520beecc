# QEMU TDA4 Emulator - Dummy Implementation

This directory contains a dummy QEMU TDA4 emulator implementation designed specifically for testing the TDA4 Emulator UI application. This is **not** a real QEMU emulator, but rather a simulation that mimics QEMU's behavior and output format.

## Overview

The dummy QEMU emulator (`qemu_tda4_emulator.cpp`) simulates:
- TDA4 platform initialization
- R5F and A72 core emulation
- Real-time log output with core-specific messages
- Interactive command processing
- Realistic memory access and interrupt simulation

## Features

### 🎯 **Core Simulation**
- **R5F Core**: Real-time tasks, interrupt handling, safety-critical operations
- **A72 Core**: Linux kernel messages, application processes, system services
- **QEMU System**: Memory access, DMA transfers, platform management

### 📊 **Log Classification**
The emulator generates logs with specific prefixes for easy parsing:
- `[R5F]` - Real-time processing unit messages
- `[A72]` - Application processor messages  
- `[QEMU]` - System-level emulator messages

### 🎮 **Interactive Commands**
- `help` - Show available commands
- `status` - Display emulation status
- `reset` - Reset emulation state
- `quit` / `exit` - Gracefully exit emulator

## Building

### Windows
```bash
# Build just the QEMU emulator
build_qemu.bat

# Or build everything
build.bat
```

### Linux/Unix
```bash
# Make the script executable
chmod +x build_qemu.sh

# Build just the QEMU emulator
./build_qemu.sh

# Or build everything
./build.sh
```

### Manual CMake Build
```bash
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --target qemu_tda4_emulator --config Release
```

## Usage

### Command Line
```bash
# Basic usage
qemu-system-arm <r5f_binary> <a72_binary>

# Example
qemu-system-arm tda4_r5f_core.exe tda4_a72_core.exe
```

### Integration with TDA4 Emulator UI

1. **Build the QEMU emulator**:
   ```bash
   cd test_binaries
   build_qemu.bat  # Windows
   # or
   ./build_qemu.sh  # Linux
   ```

2. **Copy to Assets folder**:
   ```bash
   # Windows
   copy "build\bin\qemu-system-arm.exe" "..\Assets\"
   
   # Linux
   cp build/bin/qemu-system-arm ../Assets/
   ```

3. **Use in TDA4 Emulator UI**:
   - The UI will automatically detect the QEMU binary in the Assets folder
   - Select R5F and A72 binary paths
   - Click "Start Emulation" to launch QEMU
   - View real-time logs classified by core type

## Output Examples

### Startup Sequence
```
QEMU TDA4 Emulator v2.0.0
Copyright (c) 2024 TDA4 Emulator Project
Starting emulation with:
  R5F binary: tda4_r5f_core.exe
  A72 binary: tda4_a72_core.exe

[QEMU] Initializing TDA4 platform...
[QEMU] Setting up memory regions...
[QEMU]   - DDR4 RAM: 4GB @ 0x80000000
[QEMU]   - MSMC RAM: 8MB @ 0x70000000
[QEMU]   - OCM RAM: 512KB @ 0x41000000
[QEMU] Configuring interrupt controllers...
[QEMU] Loading core binaries...
[QEMU] Starting cores...
```

### Runtime Logs
```
[R5F] Real-time task 42 executing
[R5F] Processing interrupt handler (IRQ 42)
[A72] Linux kernel message: scheduling process PID 1234
[A72] User space application started: /usr/bin/camera_app
[QEMU] IPC communication: A72 -> R5F (mailbox 0)
[QEMU] Memory access: R5F -> 0x41000000
[R5F] Safety-critical operation completed
[A72] Network interface eth0: link up, 1000Mbps
```

## Architecture

```
┌─────────────────────────────────────────┐
│           TDA4 Emulator UI              │
├─────────────────────────────────────────┤
│  QemuManager → QemuController           │
│       ↓              ↓                  │
│  Binary Path    Process Management      │
│  Resolution     stdin/stdout/stderr     │
└─────────────────┬───────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────┐
│        QEMU TDA4 Emulator               │
├─────────────────────────────────────────┤
│  • Platform Initialization             │
│  • Core Simulation (R5F/A72)           │
│  • Log Generation & Classification     │
│  • Interactive Command Processing      │
│  • Signal Handling & Graceful Shutdown │
└─────────────────────────────────────────┘
```

## Testing

### Standalone Testing
```bash
# Test with dummy binaries
echo "dummy" > test_r5f.bin
echo "dummy" > test_a72.bin
./qemu-system-arm test_r5f.bin test_a72.bin
```

### UI Integration Testing
1. Build the QEMU emulator and copy to Assets
2. Launch TDA4 Emulator UI
3. Browse for any executable files as R5F/A72 binaries
4. Start emulation and verify:
   - QEMU process launches successfully
   - Real-time logs appear in terminal views
   - Logs are properly classified by core type
   - Commands can be sent to QEMU
   - Emulation can be stopped gracefully

## Notes

- This is a **simulation** for UI testing purposes only
- The emulator validates that binary files exist but doesn't execute them
- Log output is generated randomly to simulate realistic emulation
- The emulator runs indefinitely until stopped or quit command is issued
- Signal handling ensures graceful shutdown on Ctrl+C

## Requirements

- **C++17** compatible compiler
- **CMake 3.16** or later
- **Threading support** (std::thread)
- **Platform**: Windows, Linux, macOS
