#!/bin/bash
# Build script for QEMU TDA4 Emulator
# This script builds the dummy QEMU emulator for testing the TDA4 Emulator UI

echo "========================================"
echo "Building QEMU TDA4 Emulator"
echo "========================================"

# Check if CMake is available
if ! command -v cmake &> /dev/null; then
    echo "Error: CMake is not installed or not in PATH"
    echo "Please install CMake first"
    exit 1
fi

# Check if a C++ compiler is available
if ! command -v g++ &> /dev/null && ! command -v clang++ &> /dev/null; then
    echo "Error: No C++ compiler found (g++ or clang++)"
    echo "Please install a C++ compiler first"
    exit 1
fi

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    echo "Creating build directory..."
    mkdir build
fi

# Navigate to build directory
cd build

# Configure the project
echo "Configuring CMake project..."
cmake .. -DCMAKE_BUILD_TYPE=Release
if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    cd ..
    exit 1
fi

# Build the QEMU emulator
echo "Building QEMU TDA4 Emulator..."
cmake --build . --target qemu_tda4_emulator --config Release
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    cd ..
    exit 1
fi

# Navigate back to root directory
cd ..

# Check if the executable was created
if [ -f "build/bin/qemu-system-arm" ]; then
    echo ""
    echo "========================================"
    echo "Build completed successfully!"
    echo "========================================"
    echo ""
    echo "QEMU TDA4 Emulator built as: build/bin/qemu-system-arm"
    echo ""
    echo "You can now copy this executable to the Assets folder:"
    echo "  cp build/bin/qemu-system-arm ../Assets/"
    echo ""
    echo "Or test it directly:"
    echo "  ./build/bin/qemu-system-arm test_r5f.elf test_a72.elf"
    echo ""
    
    # Make the executable file executable
    chmod +x build/bin/qemu-system-arm
    echo "Executable permissions set."
    echo ""
else
    echo "Error: Executable not found after build"
    echo "Expected: build/bin/qemu-system-arm"
    exit 1
fi

echo "Build completed successfully!"
