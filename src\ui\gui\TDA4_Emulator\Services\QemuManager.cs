using System;
using System.IO;
using System.Reflection;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.Services;

/// <summary>
/// Manages QEMU binary path resolution and validation
/// </summary>
public class QemuManager
{
    private readonly LoggingService _logger;
    private const string DefaultQemuBinaryName = "qemu-system-arm.exe";

    public QemuManager(LoggingService logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Gets the path to the QEMU binary, preferring custom path over default
    /// </summary>
    /// <param name="customQemuPath">Custom QEMU binary path (optional)</param>
    /// <returns>Path to the QEMU binary to use</returns>
    public string GetQemuBinaryPath(string? customQemuPath = null)
    {
        // If custom path is provided and valid, use it
        if (!string.IsNullOrWhiteSpace(customQemuPath) && File.Exists(customQemuPath))
        {
            _logger.LogInfo($"Using custom QEMU binary: {customQemuPath}");
            return customQemuPath;
        }

        // Fall back to default QEMU binary in Assets folder
        var defaultPath = GetDefaultQemuBinaryPath();
        if (File.Exists(defaultPath))
        {
            _logger.LogInfo($"Using default QEMU binary: {defaultPath}");
            return defaultPath;
        }

        throw new FileNotFoundException($"QEMU binary not found. Checked custom path: '{customQemuPath}' and default path: '{defaultPath}'");
    }

    /// <summary>
    /// Gets the path to the default QEMU binary in the Assets folder
    /// </summary>
    /// <returns>Path to the default QEMU binary</returns>
    public string GetDefaultQemuBinaryPath()
    {
        try
        {
            // Get the application's base directory
            var appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(appDirectory))
            {
                throw new InvalidOperationException("Could not determine application directory");
            }

            // Construct path to Assets folder
            var assetsPath = Path.Combine(appDirectory, "Assets", DefaultQemuBinaryName);
            return assetsPath;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to get default QEMU binary path: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Validates that the QEMU binary exists and is executable
    /// </summary>
    /// <param name="qemuPath">Path to the QEMU binary</param>
    /// <returns>True if the binary is valid, false otherwise</returns>
    public bool ValidateQemuBinary(string qemuPath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(qemuPath))
            {
                _logger.LogWarning("QEMU binary path is null or empty");
                return false;
            }

            if (!File.Exists(qemuPath))
            {
                _logger.LogWarning($"QEMU binary not found: {qemuPath}");
                return false;
            }

            // Additional validation could be added here (e.g., checking file signature)
            _logger.LogInfo($"QEMU binary validation successful: {qemuPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error validating QEMU binary '{qemuPath}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Checks if the default QEMU binary is available
    /// </summary>
    /// <returns>True if the default QEMU binary exists, false otherwise</returns>
    public bool IsDefaultQemuBinaryAvailable()
    {
        try
        {
            var defaultPath = GetDefaultQemuBinaryPath();
            return File.Exists(defaultPath);
        }
        catch
        {
            return false;
        }
    }
}
