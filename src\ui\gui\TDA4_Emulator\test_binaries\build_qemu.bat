@echo off
REM Build script for QEMU TDA4 Emulator
REM This script builds the dummy QEMU emulator for testing the TDA4 Emulator UI

echo ========================================
echo Building QEMU TDA4 Emulator
echo ========================================

REM Check if CMake is available
cmake --version >nul 2>&1
if errorlevel 1 (
    echo Error: CMake is not installed or not in PATH
    echo Please install CMake and add it to your PATH
    pause
    exit /b 1
)

REM Create build directory if it doesn't exist
if not exist "build" (
    echo Creating build directory...
    mkdir build
)

REM Navigate to build directory
cd build

REM Configure the project
echo Configuring CMake project...
cmake .. -DCMAKE_BUILD_TYPE=Release
if errorlevel 1 (
    echo Error: CMake configuration failed
    cd ..
    pause
    exit /b 1
)

REM Build the QEMU emulator
echo Building QEMU TDA4 Emulator...
cmake --build . --target qemu_tda4_emulator --config Release
if errorlevel 1 (
    echo Error: Build failed
    cd ..
    pause
    exit /b 1
)

REM Navigate back to root directory
cd ..

REM Check if the executable was created
if exist "build\bin\qemu-system-arm.exe" (
    echo.
    echo ========================================
    echo Build completed successfully!
    echo ========================================
    echo.
    echo QEMU TDA4 Emulator built as: build\bin\qemu-system-arm.exe
    echo.
    echo You can now copy this executable to the Assets folder:
    echo   copy "build\bin\qemu-system-arm.exe" "..\Assets\"
    echo.
    echo Or test it directly:
    echo   build\bin\qemu-system-arm.exe test_r5f.exe test_a72.exe
    echo.
) else (
    echo Error: Executable not found after build
    echo Expected: build\bin\qemu-system-arm.exe
    pause
    exit /b 1
)

pause
