#include <iostream>
#include <string>

int main() {
    std::cout << "TDA4 Test Binary - Simple Test" << std::endl;
    std::cout << "Version: 1.0.0" << std::endl;
    std::cout << "Ready to receive commands..." << std::endl;
    
    std::string line;
    while (std::getline(std::cin, line)) {
        if (line == "quit" || line == "exit") {
            std::cout << "Shutting down..." << std::endl;
            break;
        } else if (line == "status") {
            std::cout << "Status: Running" << std::endl;
        } else if (line == "help") {
            std::cout << "Available commands: status, help, quit, exit" << std::endl;
        } else if (line == "ping") {
            std::cout << "pong" << std::endl;
        } else {
            std::cout << "Unknown command: " << line << std::endl;
        }
    }
    
    return 0;
}
