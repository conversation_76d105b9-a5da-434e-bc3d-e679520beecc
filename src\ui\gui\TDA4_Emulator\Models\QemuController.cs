using System;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using ReactiveUI;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.Models;

/// <summary>
/// Controls the QEMU emulator process and manages its lifecycle
/// </summary>
public class QemuController : ReactiveObject, IDisposable
{
    private Process? _process;
    private bool _isRunning;
    private readonly CancellationTokenSource _cancellationTokenSource = new();
    private readonly QemuLogParser _logParser;
    private readonly LoggingService _logger;

    /// <summary>
    /// Path to the QEMU binary executable
    /// </summary>
    public string QemuBinaryPath { get; set; } = string.Empty;

    /// <summary>
    /// Path to the R5F core binary
    /// </summary>
    public string R5FBinaryPath { get; set; } = string.Empty;

    /// <summary>
    /// Path to the A72 core binary
    /// </summary>
    public string A72BinaryPath { get; set; } = string.Empty;

    /// <summary>
    /// Indicates whether the QEMU process is currently running
    /// </summary>
    public bool IsRunning
    {
        get => _isRunning;
        private set => this.RaiseAndSetIfChanged(ref _isRunning, value);
    }

    /// <summary>
    /// Collection of terminal output lines from QEMU
    /// </summary>
    public ObservableCollection<TerminalLine> TerminalOutput { get; } = new();

    /// <summary>
    /// Event raised when new output is received from QEMU
    /// </summary>
    public event EventHandler<TerminalLine>? OutputReceived;

    /// <summary>
    /// Event raised when the QEMU process exits
    /// </summary>
    public event EventHandler<int>? ProcessExited;

    public QemuController(QemuLogParser logParser, LoggingService logger)
    {
        _logParser = logParser;
        _logger = logger;
    }

    /// <summary>
    /// Validates that all required binary paths are set and exist
    /// </summary>
    public bool ValidateBinaryPaths()
    {
        if (string.IsNullOrWhiteSpace(QemuBinaryPath) || !File.Exists(QemuBinaryPath))
        {
            _logger.LogWarning($"QEMU binary path is invalid: '{QemuBinaryPath}'");
            return false;
        }

        if (string.IsNullOrWhiteSpace(R5FBinaryPath) || !File.Exists(R5FBinaryPath))
        {
            _logger.LogWarning($"R5F binary path is invalid: '{R5FBinaryPath}'");
            return false;
        }

        if (string.IsNullOrWhiteSpace(A72BinaryPath) || !File.Exists(A72BinaryPath))
        {
            _logger.LogWarning($"A72 binary path is invalid: '{A72BinaryPath}'");
            return false;
        }

        return true;
    }

    /// <summary>
    /// Starts the QEMU emulator process asynchronously
    /// </summary>
    public Task<bool> StartAsync()
    {
        if (IsRunning)
        {
            _logger.LogWarning("QEMU process is already running");
            return Task.FromResult(false);
        }

        if (!ValidateBinaryPaths())
        {
            var errorMessage = TerminalLine.CreateErrorOutput(CoreType.All, "Invalid binary paths - cannot start QEMU");
            AddTerminalLine(errorMessage);
            return Task.FromResult(false);
        }

        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = QemuBinaryPath,
                Arguments = $"\"{R5FBinaryPath}\" \"{A72BinaryPath}\"",
                UseShellExecute = false,
                RedirectStandardInput = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = Path.GetDirectoryName(QemuBinaryPath) ?? Environment.CurrentDirectory
            };

            _process = new Process { StartInfo = startInfo };

            // Set up event handlers for output
            _process.OutputDataReceived += OnOutputDataReceived;
            _process.ErrorDataReceived += OnErrorDataReceived;
            _process.Exited += OnProcessExited;
            _process.EnableRaisingEvents = true;

            // Start the process
            if (!_process.Start())
                return Task.FromResult(false);

            // Begin reading output asynchronously
            _process.BeginOutputReadLine();
            _process.BeginErrorReadLine();

            IsRunning = true;

            // Add system message about process start
            var startMessage = TerminalLine.CreateQemuSystemMessage(CoreType.All, 
                $"QEMU process started (PID: {_process.Id}) with R5F: {Path.GetFileName(R5FBinaryPath)}, A72: {Path.GetFileName(A72BinaryPath)}");
            AddTerminalLine(startMessage);

            _logger.LogInfo($"QEMU process started successfully with PID: {_process.Id}");
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateErrorOutput(CoreType.All, $"Failed to start QEMU process: {ex.Message}");
            AddTerminalLine(errorMessage);
            _logger.LogError($"Failed to start QEMU process: {ex.Message}");
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// Stops the QEMU process asynchronously
    /// </summary>
    public Task StopAsync()
    {
        if (!IsRunning || _process == null)
            return Task.CompletedTask;

        try
        {
            // Try graceful shutdown first
            if (!_process.HasExited)
            {
                // Send quit command to QEMU
                _process.StandardInput?.WriteLine("quit");
                _process.StandardInput?.Flush();

                // Wait for graceful shutdown
                if (!_process.WaitForExit(5000))
                {
                    // Force kill if graceful shutdown fails
                    _process.Kill();
                    _logger.LogWarning("QEMU process was forcefully terminated");
                }
            }

            var stopMessage = TerminalLine.CreateQemuSystemMessage(CoreType.All, "QEMU process stopped");
            AddTerminalLine(stopMessage);
            _logger.LogInfo("QEMU process stopped");
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateErrorOutput(CoreType.All, $"Error stopping QEMU process: {ex.Message}");
            AddTerminalLine(errorMessage);
            _logger.LogError($"Error stopping QEMU process: {ex.Message}");
        }
        finally
        {
            IsRunning = false;
            _process?.Dispose();
            _process = null;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Sends a command to the running QEMU process
    /// </summary>
    public async Task<bool> SendCommandAsync(string command)
    {
        if (!IsRunning || _process?.StandardInput == null)
            return false;

        try
        {
            await _process.StandardInput.WriteLineAsync(command);
            await _process.StandardInput.FlushAsync();

            var commandMessage = TerminalLine.CreateQemuSystemMessage(CoreType.All, $"Command sent: {command}");
            AddTerminalLine(commandMessage);

            return true;
        }
        catch (Exception ex)
        {
            var errorMessage = TerminalLine.CreateErrorOutput(CoreType.All, $"Failed to send command: {ex.Message}");
            AddTerminalLine(errorMessage);
            return false;
        }
    }

    private void OnOutputDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            var line = _logParser.ParseLogLine(e.Data, false);
            AddTerminalLine(line);
        }
    }

    private void OnErrorDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            var line = _logParser.ParseLogLine(e.Data, true);
            AddTerminalLine(line);
        }
    }

    private void OnProcessExited(object? sender, EventArgs e)
    {
        var exitCode = _process?.ExitCode ?? -1;
        IsRunning = false;

        var exitMessage = TerminalLine.CreateQemuSystemMessage(CoreType.All, $"QEMU process exited with code: {exitCode}");
        AddTerminalLine(exitMessage);

        _logger.LogInfo($"QEMU process exited with code: {exitCode}");
        ProcessExited?.Invoke(this, exitCode);
    }

    private void AddTerminalLine(TerminalLine line)
    {
        // Use dispatcher to ensure UI thread safety
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            TerminalOutput.Add(line);
            OutputReceived?.Invoke(this, line);
        });
    }

    public void Dispose()
    {
        _cancellationTokenSource.Cancel();
        _cancellationTokenSource.Dispose();

        if (IsRunning)
        {
            StopAsync().Wait(TimeSpan.FromSeconds(5));
        }

        _process?.Dispose();
    }
}
